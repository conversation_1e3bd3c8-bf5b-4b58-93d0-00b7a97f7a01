# 活动详情页面使用指南

## 🚀 快速开始

### 1. 从行程详情页面访问

在行程详情页面中，用户可以通过以下方式访问活动详情：

```typescript
// 在TripDetailPage中点击活动
handleActivityClick = (activity: Activity) => {
  router.pushUrl({
    url: 'pages/ActivityDetailPage',
    params: {
      tripId: this.trip.id,
      activityId: activity.id
    }
  });
}
```

### 2. 从每日行程页面访问

在每日行程页面中，用户可以点击具体的活动项：

```typescript
// 在DailyItineraryPage中点击活动
handleActivityClick = (activity: Activity) => {
  router.pushUrl({
    url: 'pages/ActivityDetailPage',
    params: {
      tripId: this.trip.id,
      activityId: activity.id,
      date: this.selectedDate
    }
  });
}
```

## 📱 页面功能说明

### 活动信息展示

活动详情页面会显示以下信息：

1. **活动标题**: 活动的名称
2. **活动描述**: 活动的详细说明
3. **活动图标**: 根据活动类型自动显示相应图标
4. **时间信息**: 活动的开始和结束时间
5. **地点信息**: 活动的具体位置
6. **活动时长**: 自动计算的活动持续时间
7. **活动类型**: 活动的分类标签
8. **完成状态**: 活动是否已完成

### 状态管理

用户可以通过"标记为完成"按钮来管理活动状态：

- **未完成状态**: 按钮显示为绿色，文字为"标记为完成"
- **已完成状态**: 按钮显示为灰色，文字为"标记为未完成"
- **状态同步**: 状态变更会立即同步到数据存储

### 操作功能

页面提供以下操作选项：

1. **编辑活动**: 点击编辑按钮可以修改活动信息（待实现）
2. **更多操作**: 提供额外的操作选项（待实现）
3. **返回导航**: 点击返回按钮回到上一个页面

## 🎯 使用场景

### 场景1: 查看活动详情

用户想要查看某个活动的详细信息：

1. 在行程详情或每日行程页面找到目标活动
2. 点击活动项进入活动详情页面
3. 查看活动的完整信息，包括时间、地点、描述等

### 场景2: 标记活动完成

用户完成了某个活动，想要标记为已完成：

1. 进入活动详情页面
2. 点击"标记为完成"按钮
3. 按钮变为灰色，文字变为"标记为未完成"
4. 活动状态已更新到系统中

### 场景3: 查看所属日程

用户想要了解活动所属的日程信息：

1. 在活动详情页面向下滚动
2. 查看"所属日程"部分
3. 可以看到该活动所属日程的标题、日期和活动总数

## 🔧 开发者指南

### 添加新的活动类型

如果需要添加新的活动类型，需要在以下位置进行修改：

1. **模型定义** (`TripModel.ets`):
```typescript
export enum ActivityType {
  // 现有类型...
  NEW_TYPE = 'new_type'
}
```

2. **工具函数** (`TripUtils.ets`):
```typescript
export function getActivityTypeIcon(type: ActivityType): string {
  switch (type) {
    // 现有类型...
    case ActivityType.NEW_TYPE:
      return '🆕';
    default:
      return '📍';
  }
}
```

### 扩展活动操作

要添加新的活动操作功能：

1. **在ActivityDetailPage中添加处理函数**:
```typescript
handleNewAction = () => {
  console.log('执行新操作');
  // 实现具体逻辑
}
```

2. **在UI中添加操作按钮**:
```typescript
Button('新操作')
  .onClick(this.handleNewAction)
```

### 自定义活动信息项

要添加新的活动信息展示项：

1. **在buildInfoItem中添加新项**:
```typescript
// 在活动信息部分添加
this.buildInfoItem('新字段', this.activity.newField, '🔖')
```

2. **确保Activity接口包含新字段**:
```typescript
export interface Activity {
  // 现有字段...
  newField?: string;
}
```

## 🎨 界面定制

### 修改主题色

要修改页面的主题色，可以在 `TripUtils.ets` 中调整：

```typescript
export const THEME_COLORS: ThemeColors = {
  primary: '#your-color',        // 主色调
  success: '#your-success-color', // 成功色
  // 其他颜色...
};
```

### 调整布局样式

页面布局可以通过修改以下样式属性进行调整：

- **卡片圆角**: `.borderRadius(16)`
- **内边距**: `.padding(20)`
- **外边距**: `.margin({ left: 16, right: 16 })`
- **字体大小**: `.fontSize(18)`

## 🧪 测试建议

### 功能测试

1. **数据加载测试**:
   - 验证活动信息正确加载
   - 测试不存在活动的错误处理

2. **状态管理测试**:
   - 测试完成状态切换
   - 验证状态持久化

3. **导航测试**:
   - 测试页面间跳转
   - 验证参数传递正确性

### 性能测试

1. **加载性能**:
   - 测试页面加载速度
   - 监控内存使用情况

2. **响应性能**:
   - 测试按钮点击响应时间
   - 验证状态更新速度

## 📋 常见问题

### Q: 活动信息没有正确显示？
A: 检查路由参数是否正确传递，确保 `tripId` 和 `activityId` 都有效。

### Q: 状态更新后没有保存？
A: 确保 `TripDataManager` 的 `updateActivityStatus` 方法被正确调用。

### Q: 页面样式显示异常？
A: 检查 `THEME_COLORS` 是否正确导入，确保所有样式属性都有有效值。

### Q: 返回按钮不工作？
A: 确保 `router.back()` 方法被正确调用，检查路由栈是否有上一个页面。

---

*本指南提供了活动详情页面的完整使用说明，帮助用户和开发者更好地理解和使用该功能。*
