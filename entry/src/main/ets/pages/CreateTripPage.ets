/**
 * 创建行程页面
 * 用于创建新的行程
 */

import { TripType, TripDataManager, TripInput } from '../models/TripModel';
import { TripForm, TripFormData } from '../components/TripForm';
import { THEME_COLORS, calculateDays } from '../utils/TripUtils';
import { IconText, IconConstants } from '../components/IconText';
import router from '@ohos.router';

@Entry
@Component
struct CreateTripPage {
  @State formData: TripFormData = {
    title: '',
    destination: '',
    startDate: '',
    endDate: '',
    tripType: TripType.LEISURE,
    description: '',
    isPublic: false,
    defaultActivityDuration: 60
  };
  @State isSubmitting: boolean = false;
  private tripManager: TripDataManager = new TripDataManager();

  aboutToAppear() {
    console.log('CreateTripPage: aboutToAppear 被调用');
    
    // 设置默认日期（今天和明天）
    this.setDefaultDates();
  }

  // 设置默认日期
  setDefaultDates() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    this.formData.startDate = this.formatDate(today);
    this.formData.endDate = this.formatDate(tomorrow);
  }

  // 格式化日期为 YYYY-MM-DD 格式
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 验证表单
  validateForm(): boolean {
    if (!this.formData.title.trim()) {
      console.error('CreateTripPage: 行程标题不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.destination.trim()) {
      console.error('CreateTripPage: 目的地不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.startDate) {
      console.error('CreateTripPage: 开始日期不能为空');
      // TODO: 显示错误提示
      return false;
    }

    if (!this.formData.endDate) {
      console.error('CreateTripPage: 结束日期不能为空');
      // TODO: 显示错误提示
      return false;
    }

    // 验证日期顺序
    const startDate = new Date(this.formData.startDate);
    const endDate = new Date(this.formData.endDate);
    
    if (endDate < startDate) {
      console.error('CreateTripPage: 结束日期不能早于开始日期');
      // TODO: 显示错误提示
      return false;
    }

    return true;
  }

  // 处理返回按钮
  handleBack = () => {
    router.back();
  }

  // 处理保存行程
  handleSaveTrip = () => {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    this.isSubmitting = true;

    try {
      // 计算天数
      const daysCount = calculateDays(this.formData.startDate, this.formData.endDate);
      
      // 创建行程对象
      const newTripInput: TripInput = {
        title: this.formData.title,
        destination: this.formData.destination,
        startDate: this.formData.startDate,
        endDate: this.formData.endDate,
        tripType: this.formData.tripType,
        description: this.formData.description,
        isPublic: this.formData.isPublic,
        defaultActivityDuration: this.formData.defaultActivityDuration
      };

      // 添加行程
      const tripId = this.tripManager.createTrip(newTripInput);
      
      if (tripId > 0) {
        console.log(`CreateTripPage: 成功创建行程，ID: ${tripId}`);
        
        // 显示成功提示
        // TODO: 添加Toast提示
        
        // 跳转到行程详情页面
        router.replaceUrl({
          url: 'pages/TripDetailPage',
          params: {
            tripId: tripId
          }
        });
      } else {
        console.error('CreateTripPage: 创建行程失败');
        // TODO: 显示错误提示
      }
    } catch (error) {
      console.error('CreateTripPage: 创建行程时发生错误:', error);
      // TODO: 显示错误提示
    } finally {
      this.isSubmitting = false;
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        // 返回按钮
        Button() {
          IconText({
            iconText: '←',
            fontSize: 20,
            fontColor: THEME_COLORS.textPrimary,
            iconWidth: 24,
            iconHeight: 24
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor(Color.Transparent)
        .width(40)
        .height(40)
        .onClick(this.handleBack)

        // 标题
        Text('创建行程')
          .fontSize(18)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        // 关闭按钮
        Button() {
          IconText({
            iconText: '✕',
            fontSize: 18,
            fontColor: THEME_COLORS.textSecondary,
            iconWidth: 24,
            iconHeight: 24
          })
        }
        .type(ButtonType.Circle)
        .backgroundColor(Color.Transparent)
        .width(40)
        .height(40)
        .onClick(this.handleBack)
      }
      .width('100%')
      .height(56)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(THEME_COLORS.cardBackground)

      // 表单内容
      Scroll() {
        Column() {
          TripForm({
            formData: $formData
          })
        }
        .width('100%')
        .padding({ top: 16, bottom: 100 })
      }
      .layoutWeight(1)
      .backgroundColor(THEME_COLORS.background)

      // 底部保存按钮
      Column() {
        Button(this.isSubmitting ? '保存中...' : '保存行程')
          .width('100%')
          .height(48)
          .fontSize(16)
          .fontWeight(600)
          .fontColor(Color.White)
          .backgroundColor(this.isSubmitting ? THEME_COLORS.textSecondary : THEME_COLORS.primary)
          .borderRadius(12)
          .enabled(!this.isSubmitting)
          .onClick(this.handleSaveTrip)
      }
      .width('100%')
      .padding({ left: 16, right: 16, top: 12, bottom: 12 })
      .backgroundColor(THEME_COLORS.cardBackground)
      .border({ width: { top: 1 }, color: THEME_COLORS.border })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(THEME_COLORS.background)
  }
}
