/**
 * 行程安排列表组件
 * 显示每日行程安排和活动列表
 */

import { DailyItinerary, Activity, ActivityType } from '../models/TripModel';
import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

@Component
export struct ItineraryList {
  @Prop itineraries: DailyItinerary[];
  onActivityClick?: (activity: Activity) => void;
  onDayClick?: (day: DailyItinerary) => void;

  // 获取活动类型图标
  getActivityIcon(type: ActivityType): string {
    switch (type) {
      case ActivityType.SIGHTSEEING:
        return '🏛️';
      case ActivityType.DINING:
        return '🍽️';
      case ActivityType.SHOPPING:
        return '🛍️';
      case ActivityType.TRANSPORTATION:
        return '🚗';
      case ActivityType.ACCOMMODATION:
        return '🏨';
      case ActivityType.ENTERTAINMENT:
        return '🎭';
      default:
        return '📍';
    }
  }

  // 格式化时间
  formatTime(time: string): string {
    return time;
  }

  build() {
    Column() {
      Row() {
        Text('行程概览')
          .fontSize(16)
          .fontWeight(600)
          .fontColor(THEME_COLORS.textPrimary)
          .layoutWeight(1)

        Button() {
          Text('管理')
            .fontSize(14)
            .fontColor(THEME_COLORS.primary)
        }
        .type(ButtonType.Normal)
        .backgroundColor('#E8F5E8')
        .borderRadius(16)
        .padding({ left: 12, right: 12, top: 6, bottom: 6 })
        .onClick(() => {
          console.log('点击管理按钮');
        })
      }
      .width('100%')
      .alignItems(VerticalAlign.Center)
      .margin({ bottom: 16 })

      List({ space: 12 }) {
        ForEach(this.itineraries, (day: DailyItinerary) => {
          ListItem() {
            Row() {
              // 圆形数字标识
              Stack() {
                Circle({ width: 32, height: 32 })
                  .fill(THEME_COLORS.primary)

                Text(day.dayNumber.toString())
                  .fontSize(14)
                  .fontWeight(600)
                  .fontColor(Color.White)
              }
              .width(32)
              .height(32)

              Column() {
                Text(day.title)
                  .fontSize(16)
                  .fontWeight(600)
                  .fontColor(THEME_COLORS.textPrimary)
                  .alignSelf(ItemAlign.Start)

                Text(`${day.date.replace(/-/g, '/')} • ${day.activities.length}个活动`)
                  .fontSize(14)
                  .fontColor(THEME_COLORS.textSecondary)
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)
              .margin({ left: 16 })

              Button('...')
                .fontSize(16)
                .fontColor(THEME_COLORS.textSecondary)
                .backgroundColor(Color.Transparent)
                .width(32)
                .height(32)
                .onClick(() => {
                  console.log(`点击第${day.dayNumber}天菜单`);
                })

              IconText({
                iconText: IconConstants.ARROW_RIGHT,
                fontSize: 16,
                fontColor: THEME_COLORS.textSecondary,
                iconWidth: 16,
                iconHeight: 16
              })
                .margin({ left: 8 })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)
            .padding(16)
            .backgroundColor(THEME_COLORS.cardBackground)
            .borderRadius(12)
            .shadow({ radius: 2, color: 'rgba(0, 0, 0, 0.08)', offsetY: 1 })
            .onClick(() => {
              console.log(`点击第${day.dayNumber}天行程`);
              if (this.onDayClick) {
                this.onDayClick(day);
              }
            })
          }
        })
      }
      .width('100%')
      .layoutWeight(1)
    }
    .width('100%')
    .padding({ left: 16, right: 16, bottom: 16 })
    .backgroundColor(THEME_COLORS.background)
  }
}
