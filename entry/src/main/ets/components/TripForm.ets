/**
 * 行程表单组件
 * 用于创建和编辑行程的表单界面
 */

import { TripType } from '../models/TripModel';
import { THEME_COLORS } from '../utils/TripUtils';
import { IconText, IconConstants } from './IconText';

// 行程表单数据接口
export interface TripFormData {
  title: string;
  destination: string;
  startDate: string;
  endDate: string;
  tripType: TripType;
  description: string;
  isPublic: boolean;
  defaultActivityDuration: number;
}

// 行程类型选项接口
interface TripTypeOption {
  type: TripType;
  label: string;
  icon: string;
  color: string;
}

@Component
export struct TripForm {
  @Link formData: TripFormData;
  @State showDestinationSuggestions: boolean = false;
  @State showAdvancedOptions: boolean = false;
  @State destinationSuggestions: string[] = ['法国巴黎', '日本东京', '美国纽约', '中国上海', '意大利罗马'];
  
  // 行程类型选项
  private tripTypes: TripTypeOption[] = [
    { type: TripType.LEISURE, label: '休闲旅游', icon: '🏖️', color: '#FF6B6B' },
    { type: TripType.BUSINESS, label: '商务出行', icon: '💼', color: '#4ECDC4' },
    { type: TripType.FAMILY, label: '家庭旅行', icon: '👨‍👩‍👧‍👦', color: '#45B7D1' },
    { type: TripType.LEISURE, label: '探险旅行', icon: '🎒', color: '#96CEB4' },
    { type: TripType.LEISURE, label: '文化之旅', icon: '🏛️', color: '#FFEAA7' },
    { type: TripType.LEISURE, label: '浪漫之旅', icon: '💕', color: '#FD79A8' }
  ];

  // 计算天数
  private calculateDays(): number {
    if (!this.formData.startDate || !this.formData.endDate) {
      return 0;
    }

    const startDate = new Date(this.formData.startDate);
    const endDate = new Date(this.formData.endDate);
    const timeDiff = endDate.getTime() - startDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    return daysDiff + 1; // 包含开始和结束日期
  }

  // 获取当前日期字符串
  getCurrentDate(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}/${month}/${day}`;
  }

  // 格式化日期显示
  formatDateDisplay(dateString: string): string {
    if (!dateString) return '选择日期';
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}/${month}/${day}`;
  }

  build() {
    Column() {
      // 行程标题
      Column() {
        Row() {
          Text('行程标题')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        TextInput({ placeholder: '巴黎浪漫之旅', text: this.formData.title })
          .fontSize(16)
          .fontColor(THEME_COLORS.textPrimary)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12, top: 12, bottom: 12 })
          .onChange((value: string) => {
            this.formData.title = value;
          })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 目的地
      Column() {
        Row() {
          Text('目的地')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        // 目的地输入框
        Stack() {
          TextInput({ 
            placeholder: '法国巴黎', 
            text: this.formData.destination 
          })
            .fontSize(16)
            .fontColor(THEME_COLORS.textPrimary)
            .backgroundColor('#FFF9E6')
            .borderRadius(8)
            .border({ width: 1, color: '#FFD700' })
            .padding({ left: 12, right: 40, top: 12, bottom: 12 })
            .onChange((value: string) => {
              this.formData.destination = value;
              this.showDestinationSuggestions = value.length > 0;
            })

          // 定位按钮
          Button() {
            Text('🎯')
              .fontSize(16)
              .fontColor(THEME_COLORS.primary)
          }
          .type(ButtonType.Circle)
          .backgroundColor(Color.Transparent)
          .width(32)
          .height(32)
          .position({ x: '100%', y: '50%' })
          .translate({ x: -40, y: -16 })
          .onClick(() => {
            console.log('获取当前位置');
          })
        }

        // 目的地建议列表
        if (this.showDestinationSuggestions && this.formData.destination) {
          Column() {
            ForEach(this.destinationSuggestions.filter(suggestion => 
              suggestion.toLowerCase().includes(this.formData.destination.toLowerCase())
            ), (suggestion: string) => {
              Row() {
                Text('📍')
                  .fontSize(14)
                  .margin({ right: 8 })
                
                Text(suggestion)
                  .fontSize(14)
                  .fontColor(THEME_COLORS.textPrimary)
                  .layoutWeight(1)
              }
              .width('100%')
              .height(40)
              .padding({ left: 12, right: 12 })
              .alignItems(VerticalAlign.Center)
              .onClick(() => {
                this.formData.destination = suggestion;
                this.showDestinationSuggestions = false;
              })
            })
          }
          .width('100%')
          .backgroundColor('#FFFFFF')
          .borderRadius(8)
          .border({ width: 1, color: '#E0E0E0' })
          .margin({ top: 4 })
          .constraintSize({ maxHeight: 120 })
        }
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 行程类型
      Column() {
        Row() {
          Text('行程类型')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        // 行程类型网格
        Grid() {
          ForEach(this.tripTypes, (typeInfo: TripTypeOption) => {
            GridItem() {
              Column() {
                Text(typeInfo.icon)
                  .fontSize(24)
                  .margin({ bottom: 4 })

                Text(typeInfo.label)
                  .fontSize(12)
                  .fontColor(this.formData.tripType === typeInfo.type ? 
                    THEME_COLORS.primary : THEME_COLORS.textSecondary)
                  .fontWeight(this.formData.tripType === typeInfo.type ? 600 : 400)
              }
              .width('100%')
              .height(64)
              .justifyContent(FlexAlign.Center)
              .backgroundColor(this.formData.tripType === typeInfo.type ? 
                '#E8F5E8' : '#F5F5F5')
              .borderRadius(8)
              .border({ 
                width: this.formData.tripType === typeInfo.type ? 2 : 1, 
                color: this.formData.tripType === typeInfo.type ? 
                  THEME_COLORS.primary : '#E0E0E0' 
              })
              .onClick(() => {
                this.formData.tripType = typeInfo.type;
              })
            }
          })
        }
        .columnsTemplate('1fr 1fr 1fr')
        .rowsTemplate('1fr 1fr')
        .columnsGap(8)
        .rowsGap(8)
        .width('100%')
        .height(136)
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })

      // 出行日期
      Column() {
        Row() {
          Text('出行日期')
            .fontSize(14)
            .fontColor(THEME_COLORS.textPrimary)
            .fontWeight(500)
          
          Text('*')
            .fontSize(14)
            .fontColor('#FF4444')
            .margin({ left: 4 })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 8 })

        Row() {
          // 开始日期
          Column() {
            Text('出发日期')
              .fontSize(12)
              .fontColor(THEME_COLORS.textSecondary)
              .width('100%')
              .margin({ bottom: 4 })

            Row() {
              Text(this.formatDateDisplay(this.formData.startDate))
                .fontSize(16)
                .fontColor(this.formData.startDate ? 
                  THEME_COLORS.textPrimary : THEME_COLORS.textSecondary)
                .layoutWeight(1)

              Text('📅')
                .fontSize(16)
            }
            .width('100%')
            .height(44)
            .padding({ left: 12, right: 12 })
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .alignItems(VerticalAlign.Center)
            .onClick(() => {
              console.log('选择开始日期');
              // TODO: 打开日期选择器
            })
          }
          .layoutWeight(1)

          Blank()
            .width(12)

          // 结束日期
          Column() {
            Text('返程日期')
              .fontSize(12)
              .fontColor(THEME_COLORS.textSecondary)
              .width('100%')
              .margin({ bottom: 4 })

            Row() {
              Text(this.formatDateDisplay(this.formData.endDate))
                .fontSize(16)
                .fontColor(this.formData.endDate ? 
                  THEME_COLORS.textPrimary : THEME_COLORS.textSecondary)
                .layoutWeight(1)

              Text('📅')
                .fontSize(16)
            }
            .width('100%')
            .height(44)
            .padding({ left: 12, right: 12 })
            .backgroundColor('#F5F5F5')
            .borderRadius(8)
            .alignItems(VerticalAlign.Center)
            .onClick(() => {
              console.log('选择结束日期');
              // TODO: 打开日期选择器
            })
          }
          .layoutWeight(1)
        }
        .width('100%')
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 8 })

      // 天数显示
      if (this.formData.startDate && this.formData.endDate) {
        Row() {
          Text('📅')
            .fontSize(16)
            .margin({ right: 8 })

          Text(`总共 ${this.calculateDays()} 天`)
            .fontSize(14)
            .fontColor(THEME_COLORS.primary)
            .fontWeight(500)
        }
        .width('100%')
        .justifyContent(FlexAlign.Start)
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: 16 })
      }

      // 行程描述
      Column() {
        Text('行程描述')
          .fontSize(14)
          .fontColor(THEME_COLORS.textPrimary)
          .fontWeight(500)
          .width('100%')
          .margin({ bottom: 8 })

        TextArea({
          placeholder: '探索浪漫之都巴黎的经典景点和文化魅力',
          text: this.formData.description
        })
          .fontSize(16)
          .fontColor(THEME_COLORS.textPrimary)
          .backgroundColor('#F5F5F5')
          .borderRadius(8)
          .padding({ left: 12, right: 12, top: 12, bottom: 12 })
          .height(80)
          .onChange((value: string) => {
            this.formData.description = value;
          })
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 24 })

      // 高级选项
      Column() {
        // 高级选项标题栏
        Row() {
          Text('高级选项')
            .fontSize(16)
            .fontWeight(500)
            .fontColor(THEME_COLORS.textPrimary)
            .layoutWeight(1)

          Button() {
            Text(this.showAdvancedOptions ? '🔽' : '🔼')
              .fontSize(14)
              .fontColor(THEME_COLORS.textSecondary)
          }
          .type(ButtonType.Circle)
          .backgroundColor(Color.Transparent)
          .width(32)
          .height(32)
          .onClick(() => {
            this.showAdvancedOptions = !this.showAdvancedOptions;
          })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: this.showAdvancedOptions ? 16 : 0 })

        if (this.showAdvancedOptions) {
          Column() {
            // 公开行程选项
            Row() {
              Column() {
                Text('公开行程')
                  .fontSize(16)
                  .fontWeight(500)
                  .fontColor(THEME_COLORS.textPrimary)
                  .alignSelf(ItemAlign.Start)

                Text('允许其他用户查看此行程')
                  .fontSize(14)
                  .fontColor(THEME_COLORS.textSecondary)
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)

              Toggle({ type: ToggleType.Switch, isOn: this.formData.isPublic })
                .selectedColor(THEME_COLORS.primary)
                .switchPointColor('#FFFFFF')
                .onChange((isOn: boolean) => {
                  this.formData.isPublic = isOn;
                })
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)
            .margin({ bottom: 20 })

            // 默认活动时长
            Column() {
              Text('默认活动时长（分钟）')
                .fontSize(16)
                .fontWeight(500)
                .fontColor(THEME_COLORS.textPrimary)
                .alignSelf(ItemAlign.Start)
                .margin({ bottom: 8 })

              TextInput({
                placeholder: '60',
                text: this.formData.defaultActivityDuration.toString()
              })
                .type(InputType.Number)
                .fontSize(16)
                .fontColor(THEME_COLORS.textPrimary)
                .backgroundColor('#F5F5F5')
                .borderRadius(8)
                .padding({ left: 12, right: 12 })
                .height(44)
                .onChange((value: string) => {
                  const duration = parseInt(value) || 60;
                  this.formData.defaultActivityDuration = duration;
                })
            }
            .width('100%')
            .alignItems(HorizontalAlign.Start)
          }
          .width('100%')
          .padding({ top: 8 })
        }
      }
      .width('100%')
      .alignItems(HorizontalAlign.Start)
      .margin({ bottom: 16 })
    }
    .width('100%')
    .padding({ left: 16, right: 16 })
  }
}
