import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';
import { TripDataManager, Activity, ActivityType, ActivityQueryResult } from '../main/ets/models/TripModel';

export default function activityDetailTest() {
  describe('ActivityDetailTest', function () {
    let tripManager: TripDataManager;

    beforeEach(function () {
      tripManager = new TripDataManager();
    });

    it('should get activity by id correctly', function () {
      const tripId = 1;
      const activityId = 1;
      
      const result = tripManager.getActivityById(tripId, activityId);
      
      expect(result.activity).assertNotNull();
      expect(result.dailyItinerary).assertNotNull();
      expect(result.activity?.id).assertEqual(activityId);
      expect(result.activity?.title).assertEqual('抵达戴高乐机场');
    });

    it('should return null for non-existent activity', function () {
      const tripId = 1;
      const activityId = 999;
      
      const result = tripManager.getActivityById(tripId, activityId);
      
      expect(result.activity).assertNull();
      expect(result.dailyItinerary).assertNull();
    });

    it('should update activity correctly', function () {
      const tripId = 1;
      const activityId = 1;
      const updatedData = {
        title: '更新后的活动标题',
        description: '更新后的描述'
      };
      
      const success = tripManager.updateActivity(tripId, activityId, updatedData);
      
      expect(success).assertEqual(true);
      
      const result = tripManager.getActivityById(tripId, activityId);
      expect(result.activity?.title).assertEqual('更新后的活动标题');
      expect(result.activity?.description).assertEqual('更新后的描述');
    });

    it('should delete activity correctly', function () {
      const tripId = 1;
      const activityId = 1;
      
      // 先确认活动存在
      let result = tripManager.getActivityById(tripId, activityId);
      expect(result.activity).assertNotNull();
      
      // 删除活动
      const success = tripManager.deleteActivity(tripId, activityId);
      expect(success).assertEqual(true);
      
      // 确认活动已被删除
      result = tripManager.getActivityById(tripId, activityId);
      expect(result.activity).assertNull();
    });

    it('should handle activity status update', function () {
      const tripId = 1;
      const activityId = 2; // 使用另一个活动ID避免与删除测试冲突
      
      // 更新状态为完成
      tripManager.updateActivityStatus(tripId, activityId, true);
      
      const result = tripManager.getActivityById(tripId, activityId);
      expect(result.activity?.completed).assertEqual(true);
      
      // 更新状态为未完成
      tripManager.updateActivityStatus(tripId, activityId, false);
      
      const result2 = tripManager.getActivityById(tripId, activityId);
      expect(result2.activity?.completed).assertEqual(false);
    });

    it('should validate activity types', function () {
      const validTypes = [
        ActivityType.SIGHTSEEING,
        ActivityType.DINING,
        ActivityType.SHOPPING,
        ActivityType.TRANSPORTATION,
        ActivityType.ACCOMMODATION,
        ActivityType.ENTERTAINMENT,
        ActivityType.OTHER
      ];
      
      expect(validTypes.length).assertEqual(7);
      expect(validTypes.includes(ActivityType.TRANSPORTATION)).assertEqual(true);
    });
  });
}
