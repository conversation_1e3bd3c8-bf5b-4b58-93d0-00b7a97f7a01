import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';
import { TripDataManager, Activity, ActivityType } from '../main/ets/models/TripModel';

export default function activityManagementTest() {
  describe('ActivityManagementTest', function () {
    let tripManager: TripDataManager;

    beforeEach(function () {
      tripManager = new TripDataManager();
    });

    it('should add new activity successfully', function () {
      const tripId = 1;
      const date = '2024-07-15';
      const newActivity = {
        title: '新增测试活动',
        description: '这是一个测试活动',
        startTime: '10:00',
        endTime: '11:00',
        location: '测试地点',
        type: ActivityType.SIGHTSEEING,
        completed: false
      };

      // 获取添加前的活动数量
      const beforeDetails = tripManager.getTripDetails(tripId);
      const beforeDay = beforeDetails.find(day => day.date === date);
      const beforeCount = beforeDay ? beforeDay.activities.length : 0;

      // 添加活动
      const activityId = tripManager.addActivity(tripId, date, newActivity);

      // 验证添加成功
      expect(activityId).assertLarger(0);

      // 获取添加后的活动数量
      const afterDetails = tripManager.getTripDetails(tripId);
      const afterDay = afterDetails.find(day => day.date === date);
      const afterCount = afterDay ? afterDay.activities.length : 0;

      // 验证活动数量增加了1
      expect(afterCount).assertEqual(beforeCount + 1);

      // 验证新添加的活动信息
      const addedActivity = afterDay?.activities.find(a => a.id === activityId);
      expect(addedActivity).assertNotNull();
      expect(addedActivity?.title).assertEqual(newActivity.title);
      expect(addedActivity?.description).assertEqual(newActivity.description);
      expect(addedActivity?.location).assertEqual(newActivity.location);
      expect(addedActivity?.type).assertEqual(newActivity.type);
      expect(addedActivity?.completed).assertEqual(newActivity.completed);
    });

    it('should return -1 when adding activity to non-existent date', function () {
      const tripId = 1;
      const invalidDate = '2024-12-31';
      const newActivity = {
        title: '测试活动',
        description: '测试描述',
        startTime: '10:00',
        endTime: '11:00',
        location: '测试地点',
        type: ActivityType.SIGHTSEEING,
        completed: false
      };

      const activityId = tripManager.addActivity(tripId, invalidDate, newActivity);
      expect(activityId).assertEqual(-1);
    });

    it('should update activity successfully', function () {
      const tripId = 1;
      const activityId = 1;
      const updatedData = {
        title: '更新后的活动标题',
        description: '更新后的描述',
        location: '更新后的地点',
        type: ActivityType.DINING
      };

      // 更新活动
      const success = tripManager.updateActivity(tripId, activityId, updatedData);
      expect(success).assertEqual(true);

      // 验证更新结果
      const result = tripManager.getActivityById(tripId, activityId);
      expect(result.activity).assertNotNull();
      expect(result.activity?.title).assertEqual(updatedData.title);
      expect(result.activity?.description).assertEqual(updatedData.description);
      expect(result.activity?.location).assertEqual(updatedData.location);
      expect(result.activity?.type).assertEqual(updatedData.type);
    });

    it('should delete activity successfully', function () {
      const tripId = 1;
      const activityId = 3; // 使用一个存在的活动ID

      // 获取删除前的活动
      const beforeResult = tripManager.getActivityById(tripId, activityId);
      expect(beforeResult.activity).assertNotNull();

      // 删除活动
      const success = tripManager.deleteActivity(tripId, activityId);
      expect(success).assertEqual(true);

      // 验证活动已被删除
      const afterResult = tripManager.getActivityById(tripId, activityId);
      expect(afterResult.activity).assertNull();
    });

    it('should get daily itinerary correctly', function () {
      const tripId = 1;
      const date = '2024-07-15';

      const dailyItinerary = tripManager.getDailyItinerary(tripId, date);
      
      expect(dailyItinerary).assertNotNull();
      expect(dailyItinerary?.date).assertEqual(date);
      expect(dailyItinerary?.activities).assertNotNull();
      expect(dailyItinerary?.activities.length).assertLarger(0);
    });

    it('should return null for non-existent daily itinerary', function () {
      const tripId = 1;
      const invalidDate = '2024-12-31';

      const dailyItinerary = tripManager.getDailyItinerary(tripId, invalidDate);
      expect(dailyItinerary).assertNull();
    });

    it('should get trip dates correctly', function () {
      const tripId = 1;

      const dates = tripManager.getTripDates(tripId);
      
      expect(dates).assertNotNull();
      expect(dates.length).assertLarger(0);
      expect(dates[0]).assertEqual('2024-07-15');
    });

    it('should handle activity type selection correctly', function () {
      const tripId = 1;
      const date = '2024-07-15';
      
      // 测试不同的活动类型
      const activityTypes = [
        ActivityType.SIGHTSEEING,
        ActivityType.DINING,
        ActivityType.SHOPPING,
        ActivityType.TRANSPORTATION,
        ActivityType.ACCOMMODATION,
        ActivityType.ENTERTAINMENT
      ];

      activityTypes.forEach((type, index) => {
        const newActivity = {
          title: `测试活动${index + 1}`,
          description: `测试${type}类型活动`,
          startTime: `${10 + index}:00`,
          endTime: `${11 + index}:00`,
          location: '测试地点',
          type: type,
          completed: false
        };

        const activityId = tripManager.addActivity(tripId, date, newActivity);
        expect(activityId).assertLarger(0);

        const result = tripManager.getActivityById(tripId, activityId);
        expect(result.activity?.type).assertEqual(type);
      });
    });

    it('should handle form validation scenarios', function () {
      const tripId = 1;
      const date = '2024-07-15';

      // 测试空标题
      const activityWithEmptyTitle = {
        title: '',
        description: '测试描述',
        startTime: '10:00',
        endTime: '11:00',
        location: '测试地点',
        type: ActivityType.SIGHTSEEING,
        completed: false
      };

      // 虽然数据层不会验证，但这里测试数据是否能正确存储
      const activityId = tripManager.addActivity(tripId, date, activityWithEmptyTitle);
      expect(activityId).assertLarger(0);

      const result = tripManager.getActivityById(tripId, activityId);
      expect(result.activity?.title).assertEqual('');
    });
  });
}
